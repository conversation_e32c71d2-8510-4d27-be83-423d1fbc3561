{"version": 3, "file": "css/style.css", "mappings": "AAAA,gBAAgB;AAAhB;;;6CAAA;ACAA;;6CAAA;ACAA;;;6CAAA;AAKA;;6CAAA;AAmBA;;6CAAA;AAoBA;;6CAAA;ADtCA;AACA;EACI;ADgBJ;;ACbA;AACA;EACI;EACA;ADgBJ;;ACbA;EACI;EACA;ADgBJ;;AClBA;EACI;EACA;ADgBJ;;ACbA;EACI;EACA;EAEA;EACA;EACA;EACA;EACA;EACA,cCUO;EDRP;EACA;EACA;EAEA;EACA,yBCHO;AFgBX;;ACXA;EACI;ADcJ;;ACXA;AACA;EACI;EACA;EACA;ADcJ;;ACXA;;;6CAAA;AAKA;AACA;EACI;EACA;EAEA;ADYJ;ACXI;EACI;ADaR;;ACTA;EACI;EACA;EACA;EACA;EACA;ADYJ;;ACTA;EACI;EACA;EACA;EACA;ADYJ;;ACTA;EACI;EACA;EACA;EACA;ADYJ;;ACTA;EACI;EACA;EACA;ADYJ;;ACTA;EACI;EACA;EACA;ADYJ;;ACTA;EACI;EACA;EACA;ADYJ;;ACTA;AACA;EACI;EACA;EACA;EACA;ADYJ;ACVI;EACI;ADYR;;ACRA;;6CAAA;AAIA;AACA;EACI;IACI;IACA;EDUN;ECPE;IACI;IACA;EDSN;ECNE;IACI;IACA;EDQN;ECLE;IACI;IACA;EDON;AACF;ACJA;AACA;EACI;IACI;IACA;EDMN;ECHE;IACI;IACA;EDKN;ECFE;IACI;IACA;EDIN;AACF;ACDA;;6CAAA;AAIA;AACA;EACI;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EAGA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EAEA;EAMA;EAMA;EAMA;ADlBJ;ACCI;EACI;EACA;ADCR;ACGI;EACI;EACA;ADDR;ACKI;EACI;EACA;ADHR;ACOI;EAEI;EACA;EACA;ADNR;ACQQ;EACI;EACA;ADNZ;;ACWA;;6CAAA;AAIA;AACA;;EAEI,yBC1NgB;ED2NhB,cC7MU;AFoMd;ACWI;;EACI,yBCnOc;EDoOd,cCjNM;EDkNN;ADRR;;ACaA;AACA;;EAEI,yBC5OgB;ED6OhB,cC9OkB;AFoOtB;ACaI;;EACI,yBClPc;EDmPd,cChOM;EDiON;ADVR;;ACeA;AACA;;EAEI,yBCxOU;EDyOV,cC1OU;AF8Nd;ACcI;;EACI,yBC3PY;ED4PZ,cC9OM;ED+ON;ADXR;;ACgBA;;6CAAA;AAIA;AACA;;EAEI;EACA;ADdJ;;ACiBA;;EAEI;EACA;ADdJ;;ACiBA;AACA;EACI;EACA;EACA,cC5RkB;ED6RlB;ADdJ;ACgBI;EACI,yBChSc;EDiSd,cC9QM;ED+QN;EAEA,qBCpSc;AFqRtB;ACkBI;EACI;EACA;EACA;ADhBR;;ACoBA;EACI;EACA;EACA;EACA;ADjBJ;ACmBI;EACI;EACA;EACA;EAEA;ADlBR;;ACsBA;AACA;;EAEI;ADnBJ;;ACsBA;;EAEI;ADnBJ;;AG5TA;;6CAAA;AAMA;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EAEA;EACA;EACA;EAEA;AHwTJ;AGvTI;EACI;AHyTR;AGtTI;EACI;AHwTR;;AGpTA;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EAEA;AHoTJ;AGnTI;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EAiBA;AHoSR;AGpTQ;;EAEI;EACA;AHsTZ;AGnTQ;EACI;EACA;AHqTZ;AGlTQ;EACI;EACA;AHoTZ;AGhTQ;EACI;EACA;EACA;EACA;EACA;EACA;AHkTZ;AG/SQ;EACI;AHiTZ;;AG5SA;;6CAAA;AAIA;EACI;IACI;IACA;IACA;IAEA;IAGA;IAGA;IACA;IACA;IAEA;EH0SN;EGzSM;IACI;IACA;IACA;EH2SV;AACF;AGvSA;;6CAAA;AAIA;AACA;EACI;EACA;EACA;AHwSJ;AGtSI;EACI;EACA;EACA;KAAA;AHwSR;;AGpSA;AACA;EACI;EACA;EACA;EACA;AHuSJ;;AGpSA;AACA;EACI;AHuSJ;AGrSI;EACI;EACA;EACA;EACA;EACA,yBD5IM;EC6IN,cD9IM;EC+IN;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AHuSR;AGrSQ;EACI,yBD1KQ;EC2KR,cD7JE;EC8JF;EACA;AHuSZ;AGnSQ;EACI;EACA;EACA;AHqSZ;AGlSQ;EACI;EACA;AHoSZ;;AG/RA;AACA;EACI;AHkSJ;;AG/RA;AACA;EACI;AHkSJ;;AG/RA;;6CAAA;AAIA;AACA;;EAEI;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EAEA;EACA;EACA;EACA;EAEA;EACA;EACA;EAEA;EAOA;EAMA;AHiRJ;AG7RI;;EACI;EACA;EACA;AHgSR;AG5RI;;EACI;EACA;AH+RR;AG3RI;;EACI;EACA;AH8RR;;AGvRI;;EACI;EACA;AH2RR;AGtRQ;;EACI;EACA;AHyRZ;AGrRQ;;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AHwRZ;AGnRI;;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AHsRR;;AGjRA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AHoRJ;AGjRI;EAjBJ;IAkBQ;IACA;EHoRN;AACF;AGjRI;EAEI;EACA;EACA;AHkRR;;AG7QA;EACI;EACA;EACA;EACA;AHgRJ;;AG5QA;EACI;AH+QJ;;AG5QA;;EAEI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAGA;EACA;EACA;EACA;EACA;AH6QJ;AG3QI;;EACI;EAEA;EACA;AH6QR;AG1QI;;EAEI;EACA;AH4QR;AGzQI;;EACI;EACA;EACA;AH4QR;;AGxQA;AAGA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAGA;EACA;EACA;EACA;EACA;AHuQJ;AGrQI;EACI;EAEA;EACA;AHsQR;AGnQI;EAEI;EACA;AHoQR;AGjQI;EACI;EACA;AHmQR;;AG/PA;EACI;EACA;EACA;EACA;EACA;EACA;AHkQJ;;AG/PA;EACI;EACA;EACA;EACA;EACA;AHkQJ;;AG/PA;EACI;EACA;EACA;AHkQJ;;AG5PQ;EACI;EACA;AH+PZ;AG7PQ;EAEI;AH8PZ;AG5PQ;EACI;EACA;AH8PZ;;AGxPA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;AH2PJ;AGxPI;EACI;EACA;EACA;AH0PR;;AGtPA;EACI;EACA;AHyPJ;;AGrPA;EACI;EACA;EACA;EACA;EACA;AHwPJ;AGrPI;EACI;AHuPR;AGpPQ;EAAiB;AHuPzB;AGtPQ;EAAiB;AHyPzB;AGxPQ;EAAiB;AH2PzB;AG1PQ;EAAiB;AH6PzB;AG5PQ;EAAiB;AH+PzB;AG9PQ;EAAiB;AHiQzB;AGhQQ;EAAiB;AHmQzB;AGlQQ;EAAiB;AHqQzB;;AGjQA;EACI;IACI;IACA;EHoQN;AACF;AGhQA;;EAEI;EACA;EACA;AHkQJ;;AG/PA;;EAEI;EACA;EACA;EACA;EACA;EACA;EACA;AHkQJ;AGhQI;;EACI;AHmQR;AGhQI;;EACI;EACA;AHmQR;;AG9PA;;EAEI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAGA;EACA;EACA;EACA;EACA;EACA;AH+PJ;AG7PI;;EACI;EAEA;EACA;AH+PR;AG5PI;;EAEI;EACA;AH8PR;AG3PI;;EACI;EACA;AH8PR;;AGvPI;;EACI;EACA;AH2PR;AGtPQ;;EACI;EACA;AHyPZ;AGrPQ;;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AHwPZ;AGnPI;;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AHsPR;;AGhPA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AHmPJ;AGjPI;EACI;AHmPR;AGhPI;EACI;EACA;AHkPR;;AG9OA;EACI;EACA;AHiPJ;;AG9OA;;EAEI;EACA;EACA;AHiPJ;;AG9OA;EACI;EACA;EACA;AHiPJ;;AG7OA;;EAEI;EACA;EACA;AHgPJ;AG9OI;;EACI;AHiPR;;AG7OA;EACI;AHgPJ;;AG5OA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;AH+OJ;AG5OI;EACI;AH8OR;;AG1OA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AH6OJ;AG3OI;EACI;EACA;EACA;EACA;AH6OR;AGzOI;EACI;EACA;EACA;AH2OR;AGxOI;EACI;EACA;AH0OR;;AGrOA;EACI;IACI;EHwON;AACF;AGpOA;EACI;IACI;EHsON;AACF;AGlOA;EACI;IACI;EHoON;EGjOE;IACI;EHmON;EG/NE;IACI;EHiON;EG/NM;IACI;EHiOV;EG7NM;IACI;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EH+NV;EGzNM;IACI;IACA;IACA;IACA;IACA;IACA;IACA;EH2NV;AACF;AGvNA;EACI;IACI;EHyNN;EGtNE;IACI;EHwNN;AACF;AGpNA;EAGQ;IACI;IACA;IACA;EHoNV;EGhNM;IACI;IACA;IACA;EHkNV;AACF;AG7MA;EAEI;EACA;EACA;AH8MJ;AG5MI;EACI;AH8MR;;AGzMA;;;EAGI;EACA;EACA;AH4MJ;;AIjjCA;;6CAAA;AAMA;EACI;EACA;EACA;EACA;EAWA;EA+BA;AJygCJ;AIjjCI;EACI;AJmjCR;AIjjCI;EACI;IACI;EJmjCV;AACF;AI/iCI;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EAGA;EAEA;EACA,cFpBM;AFgkCd;AIxiCI;EACI;EACA;EACA;EACA;AJ0iCR;;AItiCA;;6CAAA;AAIA;EACI;EACA;EAEA;EAEA;AJsiCJ;AIriCI;EAPJ;IAQQ;IACA;EJwiCN;AACF;;AKhnCA;EACI;ALmnCJ;AKjnCI;EACI;EACA;EACA;EACA;EACA,kEACA;EAGA;ALgnCR;AK/mCQ;EAVJ;IAWQ;IACA,2DACA;ELinCV;AACF;AK7mCI;EACI;AL+mCR;AK9mCQ;EACI;ALgnCZ;AK7mCI;EACI;AL+mCR;AK9mCQ;EACI;ALgnCZ;AK7mCI;EACI;EACA;EACA;EACA;EACA;EACA,yBH/BY;AF8oCpB;AK7mCQ;EACI;EACA;EACA,cHrCU;AFopCtB;AK3mCI;EACI;EACA;EACA;KAAA;AL6mCR;AK1mCI;EACI;EACA;EACA;EACA;EACA;AL4mCR;AK1mCI;EACI;EACA;EACA;AL4mCR;AK1mCQ;EALJ;IAMQ;EL6mCV;AACF;AK1mCI;EACI;EACA;EACA;AL4mCR;AK1mCI;EACI;AL4mCR;AK1mCI;EACI;EACA;EACA;EACA;EACA;EACA;EACA;AL4mCR;AK1mCQ;EACI;AL4mCZ;AKzmCQ;EACI;AL2mCZ;AKxmCQ;EACI;AL0mCZ;AKzmCY;EACI;EACA;EACA;KAAA;AL2mChB;AKxmCQ;EACI;AL0mCZ;AKzmCY;EACI;AL2mChB;AKzmCY;EACI;AL2mChB;;AKtmCA;EACI;EACA;EACA;EACA;ALymCJ;;AM/tCA;EACI;EACA,mBJ6BO;EI+KP;ANuhCJ;AMjuCI;EACI;EACA;EACA;ANmuCR;AMhuCI;EACI;EACA;EACA;EACA;ANkuCR;AMhuCQ;EACI;EACA,cJZU;EIaV;EACA;ANkuCZ;AM/tCQ;EACI,WJWD;EIVC;EACA;EACA;EACA;ANiuCZ;AM/tCY;EACI,cJzBM;AF0vCtB;AM5tCI;EACI;EACA;AN8tCR;AM3tCI;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AN6tCR;AM3tCQ;EACI;AN6tCZ;AM1tCQ;EAfJ;IAgBQ;EN6tCV;AACF;AM1tCI;EACI;EACA;EACA;EACA;EACA,mBJzCM;EI0CN;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AN4tCR;AM1tCQ;EAhBJ;IAiBQ;IACA;IACA;EN6tCV;AACF;AM3tCQ;EACI;EACA;EACA,mBJ7DE;AF0xCd;AM1tCQ;EACI;AN4tCZ;AM1tCY;EACI;AN4tChB;AMvtCI;EACI;EACA,mBJ9Fc;EI+Fd;EACA;EACA;EACA;EACA;EACA;EACA;EACA,cJnFM;AF4yCd;AMvtCQ;EAZJ;IAaQ;IACA;EN0tCV;AACF;AMxtCQ;EACI;EACA;EACA;AN0tCZ;AMvtCQ;EACI;EACA;EACA;EACA;EACA;ANytCZ;AMrtCI;EACI;EACA;ANutCR;AMrtCQ;EACI;EACA;EACA;EACA;EACA,cJtGD;EIuGC;EACA;EACA;ANutCZ;AMrtCY;EAVJ;IAWQ;ENwtCd;AACF;AMrtCQ;EACI;EACA;EACA;EACA,WJtHD;EIuHC;ANutCZ;AMrtCY;EACI;EACA;EACA;ANutChB;AMptCY;EACI;EACA;EACA;ANstChB;AMntCY;EACI;EACA,cJpKM;EIqKN;EACA;EACA;EACA;EACA;EACA;ANqtChB;AMntCgB;EACI;ANqtCpB;AM/sCI;EACI;EACA,mBJhLY;EIiLZ,cJnKM;EIoKN;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;ANitCR;AM/sCQ;EAdJ;IAeQ;IACA;IACA;ENktCV;AACF;AM9sCI;EACI;EACA;EACA;EACA;EACA;ANgtCR;AM7sCI;EACI;EACA;EACA;EACA;AN+sCR;AM7sCQ;EANJ;IAOQ;ENgtCV;AACF;;AM1sCA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;AN6sCJ;AM3sCI;EACI;EACA;EACA;EACA;EACA,mBJ7Oc;EI8Od;EACA;EACA;EACA;EACA;EACA,cJhOM;EIiON;EAEA;AN4sCR;AM1sCY;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AN4sChB;AMzsCY;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AN2sChB;AMtsCI;EACI;EACA;ANwsCR;;AOr+CA;AACA;EACI;EAQA;EA+BA;EAqEA;EAwCA;EA6BA;EAgCA;EAwCA;EA8CA;APysCJ;AO9+CI;EACI;EACA;EACA;APg/CR;AO5+CI;EACI;EACA;EACA;EACA;AP8+CR;AO5+CQ;EACI;EACA,cLVU;EKWV;AP8+CZ;AO3+CI;EACI;EACA;EACA;EACA;EACA;AP6+CR;AO3+CI;EACQ;EACA;EACA;EACA;AP6+CZ;AO3+CY;EANR;IAOY;EP8+Cd;AACF;AO1+CI;EACI;EACA;EACA,mBLjBM;EKkBN;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EA0BA;EAaA;EAMA;APi8CR;AO5+CQ;EACI,yBLnDQ;EKoDR,cLrDU;EKsDV;EACA;AP8+CZ;AO3+CQ;EACI;EACA;AP6+CZ;AO1+CQ;EACI;EACA;AP4+CZ;AOz+CQ;EACI;EACA;EACA;EACA;AP2+CZ;AOv+CQ;EACI;EACA;EACA;EACA,yBL5DE;EK6DF;APy+CZ;AOt+CQ;EACI,yBLrFU;AF6jDtB;AOp+CQ;EACI;UAAA;EACA;UAAA;APs+CZ;AOl+CQ;EACI;UAAA;EACA;UAAA;APo+CZ;AO/9CI;EACI;EAEA;EAEA;EAaA;EAeA;APq8CR;AOp8CQ;EACI;APs8CZ;AOj8CI;EACI;EACA;EACA;EACA;EAER;EACQ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;APk8CR;AOj8CQ;EACI;APm8CZ;AOh8CQ;EArBJ;IAsBQ;IACA;IACA;EPm8CV;AACF;AO/7CI;EACI;EACA,mBL1JM;EK2JN;EACA;EAGA;EACA;AP+7CR;AO77CQ;EAVJ;IAWQ;EPg8CV;AACF;AO97CQ;EAdJ;IAeQ;EPi8CV;AACF;AO/7CQ;EACI;APi8CZ;AO77CQ;EACI;EACA;EACA;EACA;AP+7CZ;AO17CI;EACI;EACA;EACA,mBLvLE;EKwLF;AP47CR;AO17CQ;EANJ;IAOQ;EP67CV;AACF;AO37CQ;EACI;EACA;EACA;KAAA;AP67CZ;AO17CQ;EACI;EACA;EAIA;EACA;EACA;EACA,yBLnOQ;EKqOR,cLnNE;AF2oDd;AOt7CY;EACI;EACA;EACA;EACA;EACA;KAAA;APw7ChB;AOl7CI;EACI;APo7CR;AOl7CQ;EAHJ;IAIQ;EPq7CV;AACF;AOn7CQ;EACI;EACA;EACA,WLhOD;EKiOC;EACA;EACA;EACA;APq7CZ;AOl7CQ;EACI;EACA;EACA;EACA;EACA,cL1OD;EK4OC;EACA;EACA;EACA;EACA;APm7CZ;AOh7CQ;EACI;EACA;EACA,WLxPD;EKyPC;EAEA;EACA;EACA;EACA;EACA;APi7CZ;AO56CI;EACI,mBL7RY;EK8RZ,cLhRM;AF8rDd;AO56CQ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AP86CZ;AO56CY;EAVJ;IAWQ;IACA;EP+6Cd;AACF;AO56CQ;EACI;EACA;AP86CZ;AO56CY;EACI;EACA;AP86ChB;AO16CQ;EACI;EACA;EACA;EACA;AP46CZ;AOz6CQ;EACI;EACA;EACA;EACA;EACA;AP26CZ;AOx6CQ;EACI;EACA;EACA;EACA;AP06CZ;;AQpwDA;EACI;EACA;EACA,yBN6BO;EM5BP;ARuwDJ;;AQnwDA;EACI;ARswDJ;;AQnwDA;EACI;EACA;EACA;EACA;EACA;EACA;ARswDJ;AQpwDI;EARJ;IASQ;IACA;IACA;IACA;ERuwDN;AACF;AQrwDI;EACI;EACA;EACA;EACA;EACA;ARuwDR;AQrwDQ;EACI;ARuwDZ;AQnwDI;EACI;EACA;ARqwDR;AQlwDI;EACI;EACA;EACA;EACA;EACA;EACA;KAAA;ARowDR;;AQhwDA;EACI,yBN/CkB;EMgDlB;EACA,cN9BU;AFiyDd;AQjwDI;EACI;EACA;EACA;EACA,2EACI;ARkwDZ;AQhwDQ;EAPJ;IAQI;IACA,2DACI;ERkwDV;AACF;AQ3vDQ;EACI;EACA;EACA;EACA,cNtDE;AFmzDd;AQ1vDQ;EACI;EACA;EACA;AR4vDZ;AQ1vDY;EACI;EACA;EACA;EACA;EACA;EACA,cNtFI;EMuFJ;EACA;AR4vDhB;AQ1vDgB;EACI;EACA;AR4vDpB;AQtvDgB;EACI;EACA;EACA;EACA,cNpFN;AF40Dd;AQrvDgB;EACI;ARuvDpB;AQrvDoB;EACI;EACA;EACA;EACA;EACA;ARuvDxB;AQnvDgB;EACI;EACA;EACA;EACA;EACA;EACA;ARqvDpB;AQnvDoB;EACI,cN9HJ;EM+HI;ARqvDxB;AQ7uDI;EACI;EACA;EACA;EACA;AR+uDR;AQ7uDQ;EACI;EACA;EACA;AR+uDZ;AQ5uDQ;EACI;EACA;EACA;EACA,cNrIE;AFm3Dd;AQ3uDQ;EACI;EACA;EACA;EACA;AR6uDZ;AQ1uDQ;EACI;AR4uDZ;AQ1uDY;EACI;EACA;EACA;EACA,cNtJF;AFk4Dd;AQzuDY;EACI;EACA;EACA;AR2uDhB;AQzuDgB;EACI;EACA;EACA;EACA,cNlKN;EMmKM;EACA;EACA;EACA;AR2uDpB;AQzuDoB;EACI,cN3LJ;AFs6DpB;AQzuDwB;EACI,yBN9LR;EM+LQ,cNhMN;EMiMM;AR2uD5B;AQvuDoB;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA,cN1LV;EM2LU;EACA;ARyuDxB;AQvuDwB;EACI;EACA;ARyuD5B;AQruDoB;EACI;ARuuDxB;AQhuDI;EACI;EACA;EACA;EACA;ARkuDR;AQ9tDI;EACI;EACA;EACA;EACA;ARguDR;AQ7tDI;EACI;EACA;EACA;EACA;EACA;EACA;EACA;AR+tDR;AQ7tDQ;EACI;EACA;EACA;EACA;AR+tDZ;AQ5tDQ;EACI;EACA;EACA;EACA;AR8tDZ;AQ5tDY;EANJ;IAOQ;IACA;ER+tDd;AACF;AQ7tDY;EACI;EACA;EACA;EACA;EACA;EACA;AR+tDhB;AQ7tDgB;EACI,cNjRA;EMkRA;AR+tDpB;AQ3tDY;EACI;EACA;AR6tDhB,C", "sources": ["webpack:///./resources/css/style.scss", "webpack:///./resources/css/partials/_basics.scss", "webpack:///./resources/css/partials/_colors.scss", "webpack:///./resources/css/partials/_nav.scss", "webpack:///./resources/css/partials/_hero.scss", "webpack:///./resources/css/partials/_news.scss", "webpack:///./resources/css/partials/_calendar.scss", "webpack:///./resources/css/partials/_carousel.scss", "webpack:///./resources/css/partials/_footer.scss"], "sourcesContent": ["/* ========================================\n   OK Tyr Website - Main SCSS Entry Point\n   Mobile-first responsive design system\n   ======================================== */\n\n// Core foundation styles (must be first)\n@use 'partials/basics';\n@use 'partials/colors';\n@use 'partials/grid';\n\n// Component styles\n@use 'partials/nav';\n@use 'partials/hero';\n// @use 'partials/highlighted-updates';\n@use 'partials/news';\n@use 'partials/calendar';\n@use 'partials/carousel';\n@use 'partials/footer';\n", "/* ========================================\n   Foundation Styles - Reset & Base Setup\n   ======================================== */\n\n@use 'colors' as *;\n\n/* CSS Reset & Box Model */\n* {\n    box-sizing: border-box;\n}\n\n/* Base HTML & Body Setup */\nhtml {\n    font-size: 16px; // Base font size for rem calculations\n    line-height: 1.5;\n}\n\n::selection {\n    background-color: rgba($brand-red-primary, 0.4);\n    color: rgb(255, 255, 255);\n}\n\nbody {\n    margin: 0;\n    padding: 0;\n\n    /* Typography Foundation */\n    font-family: 'Inter', sans-serif;\n    font-weight: 400;\n    font-size: 1rem;\n    line-height: 1.6;\n    color: $text-primary;\n\n    /* Font Rendering */\n    -webkit-font-smoothing: antialiased;\n    -moz-osx-font-smoothing: grayscale;\n\n    /* Background */\n    background-color: $bg-secondary;\n}\nsection {\n    padding: 8px;\n}\n\n/* Layout Container */\n.container {\n    max-width: 1440px;\n    padding: 16px;\n    margin: 40px auto;\n}\n\n/* ========================================\n   Typography System - Inter Font Scale\n   Based on 1.25 (Major Third) scale with optical adjustments\n   ======================================== */\n\n/* Heading Hierarchy */\nh1, h2, h3, h4, h5, h6 {\n    font-weight: 600;\n    margin: 0;\n\n    /* Remove margin from last child */\n    &:last-child {\n        margin-bottom: 0;\n    }\n}\n\nh1 {\n    font-size: 2.25rem;   // 36px\n    line-height: 1.2;\n    font-weight: 700;\n    letter-spacing: -0.01em;\n    margin-bottom: 1.5rem;\n}\n\nh2 {\n    font-size: 1.875rem;  // 30px\n    line-height: 1.25;\n    letter-spacing: -0.01em;\n    margin-bottom: 1.25rem;\n}\n\nh3 {\n    font-size: 1.5rem;    // 24px\n    line-height: 1.3;\n    letter-spacing: -0.005em;\n    margin-bottom: 1rem;\n}\n\nh4 {\n    font-size: 1.25rem;   // 20px\n    line-height: 1.35;\n    margin-bottom: 1rem;\n}\n\nh5 {\n    font-size: 1.125rem;  // 18px\n    line-height: 1.4;\n    margin-bottom: 0.75rem;\n}\n\nh6 {\n    font-size: 1rem;      // 16px\n    line-height: 1.45;\n    margin-bottom: 0.75rem;\n}\n\n/* Body Text */\np {\n    font-size: 1rem;      // 16px\n    line-height: 1.6;\n    font-weight: 400;\n    margin: 0 0 1rem 0;\n\n    &:last-child {\n        margin-bottom: 0;\n    }\n}\n\n/* ========================================\n   Responsive Typography Adjustments\n   ======================================== */\n\n/* Tablet & Mobile Adjustments */\n@media (max-width: 768px) {\n    h1 {\n        font-size: 1.875rem;  // 30px on tablet/mobile\n        line-height: 1.2;\n    }\n\n    h2 {\n        font-size: 1.5rem;    // 24px on tablet/mobile\n        line-height: 1.25;\n    }\n\n    h3 {\n        font-size: 1.25rem;   // 20px on tablet/mobile\n        line-height: 1.3;\n    }\n\n    h4 {\n        font-size: 1.125rem;  // 18px on tablet/mobile\n        line-height: 1.35;\n    }\n}\n\n/* Small Mobile Adjustments */\n@media (max-width: 480px) {\n    h1 {\n        font-size: 1.5rem;    // 24px on small mobile\n        line-height: 1.25;\n    }\n\n    h2 {\n        font-size: 1.25rem;   // 20px on small mobile\n        line-height: 1.3;\n    }\n\n    h3 {\n        font-size: 1.125rem;  // 18px on small mobile\n        line-height: 1.35;\n    }\n}\n\n/* ========================================\n   Button System - Consistent Interactive Elements\n   ======================================== */\n\n/* Base Button Foundation */\nbutton, .button {\n    /* Layout & Positioning */\n    display: inline-flex;\n    align-items: center;\n    justify-content: center;\n    padding: 0.75rem 1.5rem;\n\n    /* Visual Styling */\n    border: none;\n    border-radius: 20rem;\n    background: none;\n    outline: none;\n    // box-shadow: 0 2px 8px $shadow-medium;\n\n    /* Typography */\n    font-family: 'Inter', sans-serif;\n    font-size: 1rem;\n    font-weight: 700;\n    text-decoration: none;\n\n    /* Interaction */\n    cursor: pointer;\n    transform: translateY(0) scale(1);\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n\n    /* Hover Effects */\n    &:hover {\n        transition: all 0.4s ease;\n        box-shadow: 0 4px 12px $shadow-heavy;\n    }\n\n    /* Active State */\n    &:active {\n        box-shadow: 0 1px 4px $shadow-heavy;\n        transition: all 0.1s ease;\n    }\n\n    /* Focus State */\n    &:focus {\n        outline: 2px solid rgba(1, 100, 73, 0.5);\n        outline-offset: 2px;\n    }\n\n    /* Disabled State */\n    &:disabled,\n    &.disabled {\n        opacity: 0.5;\n        cursor: not-allowed;\n        transform: none !important;\n\n        &:hover {\n            background-color: initial;\n            color: initial;\n        }\n    }\n}\n\n/* ========================================\n   Button Variants - Brand Color System\n   ======================================== */\n\n/* Primary Button - Brand Red */\n.button-primary,\nbutton.primary {\n    background-color: $interactive-secondary;\n    color: $text-inverse;\n\n    &:hover {\n        background-color: $interactive-primary;\n        color: $text-inverse;\n        box-shadow: 0 4px 12px $shadow-red,\n                    0 8px 24px rgba($brand-red-primary, 0.15);\n    }\n}\n\n/* Secondary Button - Brand Green */\n.button-secondary,\nbutton.secondary {\n    background-color: $interactive-primary-hover;\n    color: $interactive-primary;\n\n\n    &:hover {\n        background-color: $interactive-primary;\n        color: $text-inverse;\n        box-shadow: 0 4px 12px $shadow-green,\n                    0 8px 24px rgba($brand-green-primary, 0.15);\n    }\n}\n\n/* Menu CTA Button - Black with Red Hover */\n.button-menu,\nbutton.menu {\n    background-color: $interactive-neutral;\n    color: $text-inverse;\n\n    &:hover {\n        background-color: $interactive-neutral-hover;\n        color: $text-inverse;\n        box-shadow: 0 4px 12px rgba($brand-red-primary, 0.4),\n                    0 8px 24px rgba($brand-red-primary, 0.2);\n    }\n}\n\n/* ========================================\n   Button Sizes & Modifiers\n   ======================================== */\n\n/* Size Variants */\n.button-sm,\nbutton.sm {\n    padding: 0.5rem 1rem;\n    font-size: 0.875rem;\n}\n\n.button-lg,\nbutton.lg {\n    padding: 1rem 2rem;\n    font-size: 1.125rem;\n}\n\n/* Outline Variants */\n.button-outline {\n    background-color: transparent;\n    border: 2px solid $interactive-primary;\n    color: $interactive-primary;\n    box-shadow: none;\n\n    &:hover {\n        background-color: $interactive-primary;\n        color: $text-inverse;\n        box-shadow: 0 4px 12px $shadow-green,\n                    0 8px 24px rgba($brand-green-primary, 0.15);\n        border-color: $interactive-primary;\n    }\n\n    &:active {\n        transform: translateY(0) scale(0.98);\n        box-shadow: 0 1px 4px $shadow-heavy;\n        transition: all 0.1s ease;\n    }\n}\n\n.button-outline-primary {\n    background-color: transparent;\n    border: 2px solid #9C2B32;\n    color: #9C2B32;\n    box-shadow: none;\n\n    &:hover {\n        background-color: #9C2B32;\n        color: white;\n        box-shadow: 0 4px 12px rgba(156, 43, 50, 0.3),\n                    0 8px 24px rgba(156, 43, 50, 0.15);\n        border-color: #9C2B32;\n    }\n}\n\n/* Layout Modifiers */\n.button-full,\nbutton.full {\n    width: 100%;\n}\n\n.button-icon,\nbutton.icon {\n    gap: 0.5rem;\n}\n\n", "/* ========================================\n   OK Tyr Color System\n   Brand colors and semantic color variables\n   ======================================== */\n\n/* ========================================\n   Brand Colors - OK Tyr Identity\n   ======================================== */\n\n// Primary Brand Colors (A1-A4 range)\n$brand-green-primary: #016449;      // Dominant brand color\n$brand-green-light: #cfe7cb;        // Light green for hover states\n$brand-green-dark: #014a37;         // Darker variant for emphasis\n\n// Marketing/Accent Colors (B range)\n$brand-red-primary: #9C2B32;        // Primary accent color\n$brand-red-light: #b8434a;          // Lighter red variant\n$brand-red-dark: #7a2228;           // Darker red variant\n\n// Complementary Colors (C range) - for future use\n$brand-blue: #2B5F9C;               // Complementary blue\n$brand-orange: #D67E37;             // Complementary orange\n$brand-purple: #6B2B9C;             // Complementary purple\n\n/* ========================================\n   Neutral Colors - Grays & Base Colors\n   ======================================== */\n\n// Pure colors\n$color-white: #ffffff;\n$color-black: #000000;\n\n// Gray scale\n$gray-50: #f8f9fa;                  // Lightest gray\n$gray-100: #f5f5f5;                 // Very light gray\n$gray-200: #eef2f6;                 // Light gray (body background)\n$gray-300: #e0e0e0;                 // Light border gray\n$gray-400: #ddd;                    // Border gray\n$gray-500: #999;                    // Medium gray\n$gray-600: #666;                    // Dark gray (secondary text)\n$gray-700: #333;                    // Very dark gray\n$gray-800: #1a1a1a;                 // Primary text color\n$gray-900: #111;                    // Darkest gray\n\n/* ========================================\n   Semantic Color Variables\n   ======================================== */\n\n// Background colors\n$bg-primary: $color-white;\n$bg-secondary: $gray-200;\n$bg-tertiary: $gray-100;\n$bg-overlay: rgba($color-black, 0.6);\n\n// Text colors\n$text-primary: $gray-800;\n$text-secondary: $gray-600;\n$text-inverse: $color-white;\n$text-brand: $brand-green-primary;\n$text-accent: $brand-red-primary;\n\n// Border colors\n$border-light: $gray-300;\n$border-medium: $gray-400;\n$border-focus: $brand-green-primary;\n\n// Interactive colors\n$interactive-primary: $brand-green-primary;\n$interactive-primary-hover: $brand-green-light;\n$interactive-secondary: $brand-red-primary;\n$interactive-secondary-hover: $brand-red-light;\n$interactive-neutral: $color-black;\n$interactive-neutral-hover: $brand-red-primary;\n\n// State colors\n$success: $brand-green-primary;\n$warning: $brand-orange;\n$error: $brand-red-primary;\n$info: $brand-blue;\n\n// Shadow colors\n$shadow-light: rgba($color-black, 0.08);\n$shadow-medium: rgba($color-black, 0.15);\n$shadow-heavy: rgba($color-black, 0.25);\n\n// Brand-specific shadows\n$shadow-green: rgba($brand-green-primary, 0.3);\n$shadow-red: rgba($brand-red-primary, 0.3);", "/* ========================================\n   Navigation System - Floating Frosted Glass Design\n   ======================================== */\n\n@use 'colors' as *;\n\n/* Navigation Container - Fixed Positioning */\n.navbar-container {\n    /* Layout & Positioning */\n    position: fixed;\n    top: 16px;\n    left: 0;\n    right: 0;\n    z-index: 100;\n\n    /* Container Sizing */\n    max-width: 1440px;\n    width: 100%;\n    margin: 0 auto;\n    padding: 0 80px;\n\n    /* Flexbox Layout */\n    display: flex;\n    justify-content: center;\n    align-items: center;\n\n    /* Smooth Show/Hide Animation */\n    transform: translateY(0);\n    transition: transform 0.4s ease-in-out;\n\n    /* Navigation States */\n    &.nav-hidden {\n        transform: translateY(-200%);\n    }\n\n    &.nav-visible {\n        transform: translateY(0);\n    }\n}\n\n/* Main Navigation Bar */\n.navbar {\n    /* Layout & Positioning */\n    position: relative;\n    width: 100%;\n    max-width: 1250px;\n    display: flex;\n    align-items: center;\n    padding: 16px;\n\n    /* Visual Styling */\n    border-radius: 10rem;\n    color: white;\n    transition: all 0.4s ease;\n\n    /* Fallback Background (for unsupported browsers) */\n    background: rgba(1, 100, 73, 0.9);\n    border: 1px solid rgba(255, 255, 255, 0.2);\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\n\n    /* Mobile Expanded State */\n    &.nav-mobile-expanded {\n        /* Layout Changes */\n        border-radius: 1.5rem;\n        flex-direction: column;\n        align-items: stretch;\n        height: 100vh;\n        max-width: none;\n        width: 100%;\n        margin: 0;\n        padding: 0;\n        overflow: hidden;\n        transform-origin: center top;\n\n        /* Header Elements Positioning */\n        .nav-logo,\n        .nav-mobile-toggle {\n            position: absolute;\n            z-index: 10;\n        }\n\n        .nav-logo {\n            top: 16px;\n            left: 16px;\n        }\n\n        .nav-mobile-toggle {\n            top: 16px;\n            right: 16px;\n        }\n\n        /* Mobile Content Area */\n        .nav-mobile-content {\n            flex: 1;\n            display: flex;\n            flex-direction: column;\n            height: 100%;\n            padding: 4rem 1rem 1rem;\n            overflow-y: auto;\n        }\n\n        .nav-mobile-items {\n            flex: 1;\n        }\n    }\n}\n\n/* ========================================\n   Frosted Glass Effect - Enhanced Visual Design\n   ======================================== */\n\n@supports (backdrop-filter: blur(16px)) or (-webkit-backdrop-filter: blur(16px)) {\n    .navbar {\n        /* Enhanced Background with Brand Tint */\n        background: rgba(1, 100, 73, 0.6);\n        border: 1px solid rgba(255, 255, 255, 0.1);\n\n        /* Inset Border Effect */\n        -webkit-box-shadow: inset 0px 0px 0px 1px rgba(255, 255, 255, 0.1);\n        -moz-box-shadow: inset 0px 0px 0px 1px rgba(255, 255, 255, 0.1);\n        box-shadow: inset 0px 0px 0px 1px rgba(255, 255, 255, 0.1),\n                    0 2px 8px rgba(0, 0, 0, 0.15);\n\n        /* Frosted Glass Backdrop Filter */\n        -webkit-backdrop-filter: blur(16px) saturate(140%) brightness(90%);\n        backdrop-filter: blur(16px) saturate(140%) brightness(90%);\n\n        /* Enhanced Mobile Expanded State */\n        &.nav-mobile-expanded {\n            background: rgba(0, 0, 0, 0.6);\n            -webkit-backdrop-filter: blur(20px) saturate(150%) brightness(80%);\n            backdrop-filter: blur(20px) saturate(150%) brightness(80%);\n        }\n    }\n}\n\n/* ========================================\n   Navigation Content Layout\n   ======================================== */\n\n/* Logo Section */\n.nav-logo {\n    flex: 0 0 auto;\n    max-width: 40px;\n    max-height: 40px;\n\n    img {\n        width: 100%;\n        height: 100%;\n        object-fit: contain;\n    }\n}\n\n/* Navigation Items Collection */\n.nav-collection {\n    display: flex;\n    align-items: center;\n    flex: 1 1 auto;\n    justify-content: center;\n}\n\n/* Call-to-Action Section */\n.nav-cta {\n    flex: 0 0 auto;\n\n    a {\n        /* Button Menu Styles */\n        display: inline-flex;\n        align-items: center;\n        justify-content: center;\n        background-color: $interactive-neutral;\n        color: $text-inverse;\n        padding: 8px 16px;\n        border: none;\n        border-radius: 20rem;\n        font-family: 'Inter', sans-serif;\n        font-size: 1rem;\n        font-weight: 500;\n        text-decoration: none;\n        cursor: pointer;\n        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n        box-shadow: 0 2px 8px $shadow-medium;\n        transform: translateY(0) scale(1);\n\n        &:hover {\n            background-color: $interactive-neutral-hover;\n            color: $text-inverse;\n            transform: translateY(-1px) scale(1.02);\n            box-shadow: 0 4px 12px rgba($brand-red-primary, 0.4),\n                        0 8px 24px rgba($brand-red-primary, 0.2);\n        }\n\n        &:active {\n            transform: translateY(0) scale(0.98);\n            box-shadow: 0 1px 4px $shadow-heavy;\n            transition: all 0.1s ease;\n        }\n\n        &:focus {\n            outline: 2px solid rgba(1, 100, 73, 0.5);\n            outline-offset: 2px;\n        }\n    }\n}\n\n/* Navigation Item Container */\n.nav-item {\n    position: relative;\n}\n\n/* Override Global Button Styles for Navbar */\n.navbar button {\n    box-shadow: none;\n}\n\n/* ========================================\n   Navigation Links & Interactive Elements\n   ======================================== */\n\n/* Base Navigation Links & Toggles */\n.nav-link,\n.nav-toggle {\n    /* Layout */\n    display: flex;\n    align-items: center;\n    gap: 0.5rem;\n    padding: 0.5rem 1rem;\n\n    /* Reset Styles */\n    background: none;\n    border: none;\n    text-decoration: none;\n    color: inherit;\n\n    /* Typography */\n    font-size: 1rem;\n    font-weight: 500;\n\n    /* Visual */\n    border-radius: 0.5rem;\n    cursor: pointer;\n    transition: background-color 0.2s ease;\n\n    /* Override Global Button Styles */\n    box-shadow: none !important;\n    transform: none !important;\n\n    /* Hover State */\n    &:hover {\n        background-color: rgba(255, 255, 255, 0.1);\n        transform: none !important;\n        box-shadow: none !important;\n    }\n\n    /* Active State */\n    &:active {\n        transform: none !important;\n        box-shadow: none !important;\n    }\n\n    /* Focus State */\n    &:focus {\n        outline: 2px solid rgba(255, 255, 255, 0.5);\n        outline-offset: 2px;\n    }\n}\n\n// Arrow animation for dropdowns (chevron to minus)\n.nav-toggle,\n.nav-toggle-level2 {\n    .nav-arrow {\n        transition: all 0.3s ease;\n        opacity: 1;\n    }\n\n    // Hide chevron and show minus when expanded\n    &[aria-expanded=\"true\"] {\n        .nav-arrow {\n            opacity: 0;\n            transform: scale(0.8);\n        }\n\n        // Add minus icon after chevron fades out\n        &::after {\n            content: '';\n            position: absolute;\n            width: 10px;\n            height: 2px;\n            background-color: currentColor;\n            border-radius: 1px;\n            opacity: 1;\n            transform: scale(1);\n            transition: all 0.3s ease 0.1s;\n            right: 0.5rem; // Position for desktop dropdowns\n        }\n    }\n\n    // Initially hide the minus icon\n    &::after {\n        content: '';\n        position: absolute;\n        width: 10px;\n        height: 2px;\n        background-color: currentColor;\n        border-radius: 1px;\n        opacity: 0;\n        transform: scale(0.8);\n        transition: all 0.3s ease;\n        right: 0.5rem; // Position for desktop dropdowns\n    }\n}\n\n// Dropdown containers\n.nav-dropdown {\n    position: absolute;\n    top: 100%;\n    left: 0;\n    min-width: 200px;\n    background: rgba(0, 0, 0, 0.9);\n    border: 1px solid rgba(255, 255, 255, 0.1);\n    border-radius: 0.5rem;\n    padding: 0.5rem 0;\n    margin-top: 0.5rem;\n    opacity: 0;\n    visibility: hidden;\n    transform: translateY(-10px);\n    transition: all 0.2s ease;\n    z-index: 1000;\n\n    // Backdrop filter for supported browsers\n    @supports (backdrop-filter: blur(16px)) or (-webkit-backdrop-filter: blur(16px)) {\n        -webkit-backdrop-filter: blur(16px) saturate(140%) brightness(90%);\n        backdrop-filter: blur(16px) saturate(140%) brightness(90%);\n    }\n\n    // Show dropdown when parent is active\n    .nav-item:hover &,\n    .nav-item.nav-active & {\n        opacity: 1;\n        visibility: visible;\n        transform: translateY(0);\n    }\n}\n\n// Level 2 dropdown positioning\n.nav-dropdown-level2 {\n    top: 0;\n    left: 100%;\n    margin-top: 0;\n    margin-left: 0.5rem;\n}\n\n// Dropdown items\n.nav-dropdown-item {\n    position: relative;\n}\n\n.nav-dropdown-link,\n.nav-toggle-level2 {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    width: 100%;\n    padding: 0.75rem 1rem;\n    text-decoration: none;\n    color: inherit;\n    background: none;\n    border: none;\n    cursor: pointer;\n    font-size: 0.9rem;\n    transition: background-color 0.2s ease;\n\n    // Override global button styles\n    box-shadow: none !important;\n    transform: none !important;\n    border-radius: 0;\n    font-weight: inherit;\n    font-family: inherit;\n\n    &:hover {\n        background-color: rgba(255, 255, 255, 0.1);\n        // Override global button hover effects\n        transform: none !important;\n        box-shadow: none !important;\n    }\n\n    &:active {\n        // Override global button active effects\n        transform: none !important;\n        box-shadow: none !important;\n    }\n\n    &:focus {\n        outline: 2px solid rgba(255, 255, 255, 0.5);\n        outline-offset: -2px;\n        background-color: rgba(255, 255, 255, 0.1);\n    }\n}\n\n/* This section is now handled above in the reorganized .nav-cta section */\n\n// Mobile Menu Toggle Button\n.nav-mobile-toggle {\n    display: none;\n    align-items: center;\n    gap: 0.5rem;\n    background: none;\n    border: none;\n    cursor: pointer;\n    color: inherit;\n    padding: 0.5rem;\n    border-radius: 0.5rem;\n    transition: background-color 0.2s ease;\n    margin-left: auto; // Push to the right side\n\n    // Override global button styles\n    box-shadow: none !important;\n    transform: none !important;\n    font-size: inherit;\n    font-weight: inherit;\n    font-family: inherit;\n\n    &:hover {\n        background-color: rgba(255, 255, 255, 0.1);\n        // Override global button hover effects\n        transform: none !important;\n        box-shadow: none !important;\n    }\n\n    &:active {\n        // Override global button active effects\n        transform: none !important;\n        box-shadow: none !important;\n    }\n\n    &:focus {\n        outline: 2px solid rgba(255, 255, 255, 0.5);\n        outline-offset: 2px;\n    }\n}\n\n.hamburger-icon {\n    display: flex;\n    flex-direction: column;\n    gap: 3px;\n    width: 18px;\n    height: 14px;\n    order: 2; // Place icon after text\n}\n\n.hamburger-line {\n    width: 100%;\n    height: 2px;\n    background-color: currentColor;\n    border-radius: 1px;\n    transition: all 0.3s ease;\n}\n\n.nav-mobile-toggle-text {\n    font-size: 0.9rem;\n    font-weight: 500;\n    order: 1; // Place text before icon\n}\n\n// Hamburger animation when menu is open (hamburger to minus)\n.nav-mobile-toggle[aria-expanded=\"true\"] {\n    .hamburger-line {\n        &:nth-child(1) {\n            opacity: 0;\n            transform: translateY(5px);\n        }\n        &:nth-child(2) {\n            // Middle line becomes the minus\n            transform: scaleX(1);\n        }\n        &:nth-child(3) {\n            opacity: 0;\n            transform: translateY(-5px);\n        }\n    }\n}\n\n// Mobile Menu Content (inside expanded nav)\n.nav-mobile-content {\n    display: none;\n    flex-direction: column;\n    flex: 1;\n    width: 100%;\n    opacity: 0;\n    transform: translateY(20px);\n    transition: all 0.4s ease 0.2s; // Delay for smooth entrance\n\n    // Show when nav is expanded\n    .nav-mobile-expanded & {\n        display: flex;\n        opacity: 1;\n        transform: translateY(0);\n    }\n}\n\n.nav-mobile-items {\n    flex: 1;\n    overflow-y: auto;\n}\n\n// Mobile Menu Items\n.nav-mobile-item {\n    border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n    padding: 0 1rem;\n    opacity: 0;\n    transform: translateY(10px);\n    animation: none; // Reset animation initially\n\n    // Apply animation when nav is expanded\n    .nav-mobile-expanded & {\n        animation: fadeInUp 0.4s ease forwards;\n\n        // Stagger animation for each item\n        &:nth-child(1) { animation-delay: 0.3s; }\n        &:nth-child(2) { animation-delay: 0.4s; }\n        &:nth-child(3) { animation-delay: 0.5s; }\n        &:nth-child(4) { animation-delay: 0.6s; }\n        &:nth-child(5) { animation-delay: 0.7s; }\n        &:nth-child(6) { animation-delay: 0.8s; }\n        &:nth-child(7) { animation-delay: 0.9s; }\n        &:nth-child(8) { animation-delay: 1.0s; }\n    }\n}\n\n@keyframes fadeInUp {\n    to {\n        opacity: 1;\n        transform: translateY(0);\n    }\n}\n\n// Split header for items with submenus\n.nav-mobile-item-header,\n.nav-mobile-subitem-header {\n    display: flex;\n    align-items: center;\n    width: 100%;\n}\n\n.nav-mobile-item-header .nav-mobile-link,\n.nav-mobile-subitem-header .nav-mobile-link {\n    flex: 1;\n    padding: 1rem 0;\n    text-decoration: none;\n    color: inherit;\n    font-size: 1.1rem;\n    font-weight: 500;\n    transition: color 0.2s ease;\n\n    &:hover {\n        color: rgba(255, 255, 255, 0.8);\n    }\n\n    &:focus {\n        outline: 2px solid rgba(255, 255, 255, 0.5);\n        outline-offset: 2px;\n    }\n}\n\n// Toggle buttons for submenus\n.nav-mobile-toggle-item,\n.nav-mobile-toggle-subitem {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    width: 48px;\n    height: 48px;\n    background: none;\n    border: none;\n    cursor: pointer;\n    color: inherit;\n    border-radius: 0.5rem;\n    transition: background-color 0.2s ease;\n\n    // Override global button styles\n    padding: 0 !important;\n    box-shadow: none !important;\n    transform: none !important;\n    font-size: inherit;\n    font-weight: inherit;\n    font-family: inherit;\n\n    &:hover {\n        background-color: rgba(255, 255, 255, 0.1);\n        // Override global button hover effects\n        transform: none !important;\n        box-shadow: none !important;\n    }\n\n    &:active {\n        // Override global button active effects\n        transform: none !important;\n        box-shadow: none !important;\n    }\n\n    &:focus {\n        outline: 2px solid rgba(255, 255, 255, 0.5);\n        outline-offset: 2px;\n    }\n}\n\n// Icon swap animation: chevron to minus (active)\n.nav-mobile-toggle-item,\n.nav-mobile-toggle-subitem {\n    .nav-arrow {\n        transition: all 0.3s ease;\n        opacity: 1;\n    }\n\n    // Hide chevron and show minus when expanded\n    &[aria-expanded=\"true\"] {\n        .nav-arrow {\n            opacity: 0;\n            transform: scale(0.8);\n        }\n\n        // Add minus icon after chevron fades out\n        &::after {\n            content: '';\n            position: absolute;\n            width: 12px;\n            height: 2px;\n            background-color: currentColor;\n            border-radius: 1px;\n            opacity: 1;\n            transform: scale(1);\n            transition: all 0.3s ease 0.1s; // Slight delay for smooth transition\n        }\n    }\n\n    // Initially hide the minus icon\n    &::after {\n        content: '';\n        position: absolute;\n        width: 12px;\n        height: 2px;\n        background-color: currentColor;\n        border-radius: 1px;\n        opacity: 0;\n        transform: scale(0.8);\n        transition: all 0.3s ease;\n    }\n}\n\n\n// Regular mobile links (no submenus)\n.nav-mobile-link {\n    display: flex;\n    align-items: center;\n    width: 100%;\n    padding: 1rem 0;\n    text-decoration: none;\n    color: inherit;\n    font-size: 1.1rem;\n    font-weight: 500;\n    transition: color 0.2s ease;\n\n    &:hover {\n        color: rgba(255, 255, 255, 0.8);\n    }\n\n    &:focus {\n        outline: 2px solid rgba(255, 255, 255, 0.5);\n        outline-offset: 2px;\n    }\n}\n\n.nav-mobile-link-level1 {\n    font-size: 1.2rem;\n    font-weight: 600;\n}\n\n.nav-mobile-link-level2,\n.nav-mobile-subitem-header .nav-mobile-link {\n    padding-left: 1rem;\n    font-size: 1rem;\n    font-weight: 400;\n}\n\n.nav-mobile-link-level3 {\n    padding-left: 2rem;\n    font-size: 0.9rem;\n    font-weight: 400;\n}\n\n// Mobile Submenus\n.nav-mobile-submenu,\n.nav-mobile-subsubmenu {\n    max-height: 0;\n    overflow: hidden;\n    transition: max-height 0.3s ease;\n\n    &.nav-mobile-submenu-active {\n        max-height: 500px; // Adjust based on content\n    }\n}\n\n.nav-mobile-subitem {\n    border-bottom: 1px solid rgba(255, 255, 255, 0.05);\n}\n\n// Mobile CTA at bottom\n.nav-mobile-cta {\n    margin-top: auto;\n    padding: 1rem;\n    width: 100%;\n    opacity: 0;\n    text-align: center;\n    transform: translateY(20px);\n    animation: none; // Reset animation initially\n\n    // Apply animation when nav is expanded\n    .nav-mobile-expanded & {\n        animation: fadeInUp 0.4s ease 1.1s forwards; // Animate in last\n    }\n}\n\n.nav-mobile-cta-button {\n    /* Button Menu + Full Width Styles */\n    display: inline-flex;\n    align-items: center;\n    justify-content: center;\n    background-color: #000000;\n    color: white;\n    padding: 16px 24px;\n    border: none;\n    border-radius: 20rem;\n    font-family: 'Inter', sans-serif;\n    font-size: 1rem;\n    font-weight: 500;\n    text-decoration: none;\n    cursor: pointer;\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\n    transform: translateY(0) scale(1);\n    width: 100%;\n\n    &:hover {\n        background-color: #9C2B32;\n        color: white;\n        transform: translateY(-1px) scale(1.02);\n        box-shadow: 0 4px 12px rgba(156, 43, 50, 0.4),\n                    0 8px 24px rgba(156, 43, 50, 0.2);\n    }\n\n    &:active {\n        transform: translateY(0) scale(0.98);\n        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);\n        transition: all 0.1s ease;\n    }\n\n    &:focus {\n        outline: 2px solid rgba(1, 100, 73, 0.5);\n        outline-offset: 2px;\n    }\n}\n\n//Decrease padding after 1600px\n@media (max-width: 1600px) {\n    .navbar-container {\n        padding: 0 20px;\n    }\n}\n\n//Remove CTA after 1200px\n@media (max-width: 1200px) {\n    .nav-cta {\n        display: none;\n    }\n}\n\n// Mobile navigation breakpoint\n@media (max-width: 1024px) {\n    .nav-desktop {\n        display: none;\n    }\n\n    .nav-mobile-toggle {\n        display: flex;\n    }\n\n    // Ensure proper flex layout in mobile mode\n    .navbar {\n        justify-content: space-between;\n\n        .nav-logo {\n            flex: 0 0 auto;\n        }\n\n        // When expanded, take full viewport\n        &.nav-mobile-expanded {\n            position: fixed;\n            top: 20px;\n            left: 20px;\n            right: 20px;\n            bottom: 20px;\n            max-width: none;\n            width: auto;\n            height: auto;\n            z-index: 1000;\n        }\n    }\n\n    // Adjust container for expanded state\n    .navbar-container {\n        &.nav-expanded {\n            position: fixed;\n            top: 0;\n            left: 0;\n            right: 0;\n            bottom: 0;\n            padding: 20px;\n            z-index: 1000;\n        }\n    }\n}\n\n@media (max-width: 768px) {\n    .navbar-container {\n        padding: 0 20px; // Reduced padding on mobile\n    }\n\n    nav {\n        padding: 12px; // Reduced nav padding on mobile\n    }\n}\n\n// Enhanced hover effects for desktop\n@media (min-width: 769px) {\n    .nav-item {\n        // Hover to show dropdown\n        &:hover .nav-dropdown {\n            opacity: 1;\n            visibility: visible;\n            transform: translateY(0);\n        }\n\n        // Nested hover for level 2\n        .nav-dropdown-item:hover .nav-dropdown-level2 {\n            opacity: 1;\n            visibility: visible;\n            transform: translateY(0);\n        }\n    }\n}\n\n// Animation improvements\n.nav-dropdown {\n    // Smooth entrance animation\n    animation-duration: 0.2s;\n    animation-timing-function: ease-out;\n    animation-fill-mode: both;\n\n    &.nav-dropdown-level2 {\n        animation-delay: 0.1s; // Slight delay for nested dropdowns\n    }\n}\n\n// Focus management for accessibility\n.nav-toggle:focus,\n.nav-toggle-level2:focus,\n.nav-dropdown-link:focus {\n    outline: 2px solid rgba(255, 255, 255, 0.8);\n    outline-offset: 2px;\n    background-color: rgba(255, 255, 255, 0.15);\n}", "/* ========================================\n   Hero Section - Main landing area\n   ======================================== */\n\n@use 'colors' as *;\n\n.hero {\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n    align-items: center;\n\n    h1 {\n        font-size: 3em;\n    }\n    @media (max-width: 768px) {\n        h1 {\n            font-size: 2em;\n        }\n    }\n\n    /* Hero Content Container */\n    &-content {\n        height: 90vh;\n        max-height: 800px;\n        min-height: 600px;\n        max-width: 1600px;\n        margin-bottom: 4rem;;\n        width: 100%;\n        position: relative;\n        overflow: hidden;\n        border-radius: 24px;\n        padding: 2rem;\n\n        /* Layout */\n        display: flex;\n        flex-direction: column;\n        justify-content: flex-end;\n        align-items: flex-start;\n\n        /* Background & Visual Effects */\n        background-size: cover !important;\n        background-position: center center;\n        background: linear-gradient(180deg, rgba($color-black, 0) 0%, $bg-overlay 100%),\n                    linear-gradient(0deg, $bg-overlay, $bg-overlay),\n                    linear-gradient(0deg, $brand-green-primary, $brand-green-primary);\n        background-blend-mode: normal, normal, color, normal, normal;\n\n        /* Typography */\n        color: $text-inverse;\n    }\n\n    /* Call-to-Action Section */\n    &-cta {\n        margin-top: 2rem;\n        display: flex;\n        gap: 1rem;\n        flex-wrap: wrap;\n    }\n}\n\n/* ========================================\n   Banner Content Area\n   ======================================== */\n\n.banner {\n    width: 50vw;\n    padding: 2rem;\n\n    /* Typography Styles - inherit from global styles */\n\n    /* Mobile Responsive */\n    @media (max-width: 768px) {\n        width: 100%;\n        padding: 0;\n    }\n}\n\n", "@use 'colors' as *;\n\n// HOMEPAGE\n.news {\n    margin-bottom: 3rem;\n\n    .news-grid {\n        display: grid;\n        gap: 2rem;\n        margin-bottom: 3rem;\n        grid-template-columns: 1fr 1fr;\n        grid-template-areas: \n        \"news-featured news-list\"\n        \"news-cta news-cta\";\n\n        /* Mobile: Stack vertically */\n        @media (max-width: 768px) { \n            grid-template-columns: 1fr;\n            grid-template-areas: \n            \"news-featured\"\n            \"news-list\"\n            \"news-cta\";\n        }\n    }\n    .news-featured {\n        grid-area: news-featured;\n        h2 {\n            margin-bottom: 1.5rem;\n        }\n    }\n    .news-list {\n        grid-area: news-list;\n        h3 {\n            margin-bottom: 1.5rem;\n        }\n    }\n    .news-placeholder {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        width: 100%;\n        height: 100%;\n        background-color: $brand-green-light;\n\n        svg {\n            width: 2rem;\n            height: 2rem;\n            color: $brand-green-primary;\n        }\n    }\n\n    img {\n        width: 100%;\n        height: auto;\n        object-fit: cover;\n        // border-radius: 1rem;\n    }\n    time {\n        display: block;\n        font-size: 0.875rem;\n        opacity: 0.9;\n        margin-bottom: 0.5rem;\n        font-weight: 500;\n    }\n    h2 {\n        margin: 0;\n        font-size: 1.5rem;\n        line-height: 1.3;\n\n        @media (min-width: 768px) {\n            font-size: 1.875rem;\n        }\n    }\n\n    p {\n        margin: 0.75rem 0 0 0;\n        opacity: 0.9;\n        line-height: 1.5;\n    }\n    a {\n        color: black;\n    }\n    .news-listing {\n        display: grid;\n        gap: 1rem;\n        grid-template-columns: 1fr 3fr;\n        grid-template-areas: \"news-image news-content\";\n        padding: 1rem;\n        border-top: 1px solid black;\n        transition: all 0.3s ease;\n\n        &:last-child {\n            border-bottom: 1px solid black;\n        }\n\n        &:hover {\n            transform: translateY(-4px);\n        }\n\n        .news-image {\n            grid-area: news-image;\n            img {\n                width: 100%;\n                height: auto;\n                object-fit: cover;\n            }\n        }\n        .news-content {\n            grid-area: news-content;\n            h3 {\n                margin-bottom: 1rem;\n            }\n            p {\n                margin-bottom: 1rem;\n            }\n        }\n    }\n}\n.news-cta {\n    grid-area: news-cta;\n    display: flex;\n    justify-content: flex-end;\n    margin-bottom: 2rem;\n}\n\n\n// NEWS LISTING PAGE\n\n\n// NEWS STORY PAGE", "@use 'colors' as *;\n\n// HOMEPAGE - PILL CALENDAR\n\n.calendar-pills {\n    padding: 2rem 0;\n    background: $bg-secondary;\n\n    .container {\n        max-width: 1440px;\n        margin: 0 auto;\n        padding: 0 1rem;\n    }\n\n    .calendar-header {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        margin-bottom: 1.5rem;\n\n        h3 {\n            margin: 0;\n            color: $text-brand;\n            font-size: 1.5rem;\n            font-weight: 600;\n        }\n\n        .calendar-view-all {\n            color: $text-secondary;\n            text-decoration: none;\n            font-size: 0.875rem;\n            font-weight: 500;\n            transition: color 0.2s ease;\n\n            &:hover {\n                color: $interactive-primary;\n            }\n        }\n    }\n\n    .carousel-wrapper {\n        position: relative;\n        overflow: hidden;\n    }\n\n    .pills-scroll {\n        display: flex;\n        gap: 1rem;\n        overflow-x: auto;\n        scroll-behavior: smooth;\n        scroll-snap-type: x mandatory;\n        -webkit-overflow-scrolling: touch;\n        scrollbar-width: none;\n        -ms-overflow-style: none;\n        padding: 0.5rem 0 1rem 0;\n\n        &::-webkit-scrollbar {\n            display: none;\n        }\n\n        @media (max-width: 640px) {\n            gap: 0.75rem;\n        }\n    }\n\n    .event-pill {\n        flex: 0 0 auto;\n        display: flex;\n        align-items: center;\n        gap: 0.75rem;\n        background: $bg-primary;\n        border-radius: 50px;\n        padding: 0.75rem 1.25rem;\n        text-decoration: none;\n        color: inherit;\n        transition: all 0.3s ease;\n        scroll-snap-align: start;\n        position: relative;\n        min-width: 200px;\n        box-shadow: 0 2px 8px $shadow-light;\n\n        @media (max-width: 640px) {\n            min-width: 180px;\n            padding: 0.625rem 1rem;\n            gap: 0.625rem;\n        }\n\n        &:hover {\n            transform: translateY(-2px);\n            box-shadow: 0 4px 16px $shadow-medium;\n            background: $bg-primary;\n        }\n\n        &--multiple {\n            background: linear-gradient(135deg, $bg-primary 0%, $brand-green-light 100%);\n\n            &:hover {\n                background: linear-gradient(135deg, $bg-primary 0%, $brand-green-light 100%);\n            }\n        }\n    }\n\n    .pill-date {\n        flex-shrink: 0;\n        background: $brand-green-primary;\n        border-radius: 50%;\n        width: 44px;\n        height: 44px;\n        display: flex;\n        flex-direction: column;\n        align-items: center;\n        justify-content: center;\n        color: $text-inverse;\n\n        @media (max-width: 640px) {\n            width: 40px;\n            height: 40px;\n        }\n\n        .pill-day {\n            font-size: 0.875rem;\n            font-weight: 700;\n            line-height: 1;\n        }\n\n        .pill-month {\n            font-size: 0.625rem;\n            font-weight: 500;\n            text-transform: uppercase;\n            line-height: 1;\n            margin-top: 1px;\n        }\n    }\n\n    .pill-content {\n        flex: 1;\n        min-width: 0;\n\n        h4 {\n            margin: 0 0 0.25rem 0;\n            font-size: 0.875rem;\n            font-weight: 600;\n            line-height: 1.3;\n            color: $text-primary;\n            white-space: nowrap;\n            overflow: hidden;\n            text-overflow: ellipsis;\n\n            @media (max-width: 640px) {\n                font-size: 0.8125rem;\n            }\n        }\n\n        .pill-meta {\n            display: flex;\n            gap: 0.5rem;\n            font-size: 0.75rem;\n            color: $text-secondary;\n            line-height: 1.2;\n\n            span {\n                white-space: nowrap;\n                overflow: hidden;\n                text-overflow: ellipsis;\n            }\n\n            span:not(:last-child)::after {\n                content: '•';\n                margin-left: 0.5rem;\n                opacity: 0.5;\n            }\n\n            .pill-source {\n                background: rgba($brand-green-primary, 0.1);\n                color: $brand-green-primary;\n                padding: 0.125rem 0.375rem;\n                border-radius: 10px;\n                font-size: 0.625rem;\n                font-weight: 600;\n                text-transform: uppercase;\n                letter-spacing: 0.5px;\n\n                &::before {\n                    content: none; /* Remove the bullet point */\n                }\n            }\n        }\n    }\n\n    .pill-indicator {\n        flex-shrink: 0;\n        background: $brand-red-primary;\n        color: $text-inverse;\n        border-radius: 50%;\n        width: 20px;\n        height: 20px;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        font-size: 0.625rem;\n        font-weight: 700;\n        margin-left: 0.5rem;\n\n        @media (max-width: 640px) {\n            width: 18px;\n            height: 18px;\n            font-size: 0.5625rem;\n        }\n    }\n\n    /* Navigation for calendar pills - reuse carousel styles */\n    .carousel-meta {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        gap: 0.5rem;\n        padding: 1rem 0;\n    }\n\n    .carousel-nav {\n        display: flex;\n        flex-direction: row;\n        justify-content: flex-start;\n        gap: 0.5rem;\n\n        @media (max-width: 640px) {\n            display: none; /* Hide on mobile, rely on touch scrolling */\n        }\n    }\n}\n\n// CALENDAR PAGE - Legacy styles (if needed)\n\n.calendar-event {\n    display: grid;\n    grid-template-columns: 1fr 3fr;\n    grid-template-areas: \"event-date event-details\";\n    align-items: flex-start;\n    gap: 1rem;\n    width: 100%;\n    height: 100%;\n\n    .event-date {\n        grid-area: event-date;\n        margin: 1rem;\n        padding: 1rem;\n        flex-shrink: 0;\n        background: $brand-green-primary;\n        border-radius: 8px;\n        display: flex;\n        flex-direction: column;\n        align-items: center;\n        justify-content: center;\n        color: $text-inverse;\n        position: relative;\n\n        /* Card stack effect for multiple events */\n        &.multiple-events {\n            &::before {\n                content: '';\n                position: absolute;\n                top: -3px;\n                left: -3px;\n                right: 3px;\n                bottom: 3px;\n                background: rgba($brand-green-primary, 0.3);\n                border-radius: 8px;\n                z-index: -1;\n            }\n\n            &::after {\n                content: '';\n                position: absolute;\n                top: -6px;\n                left: -6px;\n                right: 6px;\n                bottom: 6px;\n                background: rgba($brand-green-primary, 0.15);\n                border-radius: 8px;\n                z-index: -2;\n            }\n        }\n    }\n\n    .event-details {\n        grid-area: event-details;\n        padding: 0.5rem;\n    }\n}\n\n// CALENDAR PAGE", "@use 'colors' as *;\n/* News Carousel - Mobile-First Implementation */\n.carousel {\n    padding: 3rem 0;\n\n    .container {\n        max-width: 1440px;\n        margin: 0 auto;\n        padding: 0 1rem;\n    }\n\n    /* Header with navigation */\n    .carousel-header {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        margin-bottom: 2rem;\n\n        h3 {\n            margin: 0;\n            color: $text-brand;\n            font-size: 1.5rem;\n        }\n    }\n    .carousel-meta {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        gap: 0.5rem;\n        padding: 1rem 0;\n    }\n    .carousel-nav {\n            display: flex;\n            flex-direction: row;\n            justify-content: flex-start;\n            gap: 0.5rem;\n\n            @media (max-width: 640px) {\n                display: none; /* Hide on mobile, rely on touch scrolling */\n            }\n        }\n\n    /* Navigation buttons - Ultra simple with SVG arrows */\n    .carousel-btn {\n        width: 44px;\n        height: 44px;\n        background: $bg-primary;\n        border: none;\n        border-radius: 50%;\n        cursor: pointer;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        transition: background-color 0.2s ease;\n\n        /* Override global button styles */\n        padding: 0;\n        box-shadow: none;\n        transform: none;\n        outline: none;\n\n        &:hover:not(:disabled) {\n            background-color: $interactive-primary-hover;\n            color: $interactive-primary;\n            box-shadow: none;\n            transform: none;\n        }\n\n        &:active {\n            box-shadow: none;\n            transform: none;\n        }\n\n        &:focus {\n            box-shadow: none;\n            outline: none;\n        }\n\n        &:disabled {\n            opacity: 0.4;\n            cursor: not-allowed;\n            box-shadow: none;\n            transform: none;\n        }\n\n        /* SVG arrow icons */\n        &::before {\n            content: '';\n            width: 16px;\n            height: 16px;\n            background-color: $color-black;\n            transition: background-color 0.2s ease;\n        }\n\n        &:hover:not(:disabled)::before {\n            background-color: $interactive-primary;\n        }\n\n        /* Previous arrow (left) */\n        &--prev::before {\n            mask: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='currentColor'%3E%3Cpath d='M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z'/%3E%3C/svg%3E\") no-repeat center;\n            mask-size: contain;\n        }\n\n        /* Next arrow (right) */\n        &--next::before {\n            mask: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='currentColor'%3E%3Cpath d='M8.59 16.59L10 18l6-6-6-6-1.41 1.41L13.17 12z'/%3E%3C/svg%3E\") no-repeat center;\n            mask-size: contain;\n        }\n    }\n\n    /* Carousel wrapper - allows visible overflow for shadows/effects */\n    .carousel-wrapper {\n        position: relative;\n        // overflow: hidden; /* Hide overflow to contain gradient effect */\n        padding: 1rem 0;\n\n        /* Gradient fade-out effect on right side */\n        // &::after {\n        //     content: '';\n        //     position: absolute;\n        //     top: 0;\n        //     right: 0;\n        //     bottom: 0;\n        //     width: 80px; /* Width of fade effect */\n        //     background: linear-gradient(to right, transparent, $bg-secondary);\n        //     pointer-events: none; /* Allow clicks through the gradient */\n        //     z-index: 10;\n        // }\n\n        /* Optional: Add fade effect on left side too for symmetry */\n        // &::before {\n        //     content: '';\n        //     position: absolute;\n        //     top: 0;\n        //     left: 0;\n        //     bottom: 0;\n        //     width: 20px; /* Smaller fade on left */\n        //     background: linear-gradient(to left, transparent, $bg-secondary);\n        //     pointer-events: none;\n        //     z-index: 10;\n        //     opacity: 0; /* Start hidden, can be shown when scrolled */\n        //     transition: opacity 0.3s ease;\n        // }\n\n        /* Show left fade when carousel is scrolled */\n        &.scrolled::before {\n            opacity: 1;\n        }\n    }\n\n    /* Scrollable container */\n    .carousel-scroll {\n        display: flex;\n        gap: 1.5rem;\n        overflow-x: auto;\n        border-right: 1px solid hsl(210, 31%, 65%);\n        -webkit-box-shadow:inset -8px 0 8px 0 #eef2f6;\nbox-shadow:inset -8px 0 8px 0 #eef2f6;\n        /* Note: overflow-y is automatically set to hidden when overflow-x is auto */\n        scroll-behavior: smooth;\n        scroll-snap-type: x mandatory;\n        -webkit-overflow-scrolling: touch; /* iOS momentum scrolling */\n        scrollbar-width: none; /* Firefox */\n        -ms-overflow-style: none; /* IE/Edge */\n        padding: 2rem 1rem;\n        margin: -1rem;\n\n        /* Hide scrollbar in WebKit browsers */\n        &::-webkit-scrollbar {\n            display: none;\n        }\n\n        @media (max-width: 640px) {\n            gap: 1rem;\n            padding: 1rem 0.5rem;\n            margin: -1rem -0.5rem;\n        }\n    }\n\n    /* Card base styles */\n    .carousel-card {\n        flex: 0 0 300px;\n        background: $bg-primary;\n        border-radius: 8px;\n        overflow: hidden;\n\n        // box-shadow: 0 2px 12px $shadow-light;\n        scroll-snap-align: start;\n        transition: all 0.3s ease;\n\n        @media (max-width: 768px) {\n            flex: 0 0 280px;\n        }\n\n        @media (max-width: 480px) {\n            flex: 0 0 260px;\n        }\n\n        &:hover {\n            transform: translateY(-2px);\n            // box-shadow: 0 8px 24px rgba($color-black, 0.12);\n        }\n\n        a {\n            display: block;\n            text-decoration: none;\n            color: inherit;\n            height: 100%;\n        }\n    }\n\n    /* Card image */\n    .card-image {\n        height: 150px;\n        overflow: hidden;\n        background: $gray-50;\n        position: relative;\n\n        @media (max-width: 480px) {\n            height: 160px;\n        }\n\n        img {\n            width: 100%;\n            height: 100%;\n            object-fit: cover;\n        }\n\n        .card-placeholder {\n            width: 100%;\n            height: 100%;\n            // position: absolute;\n            // top: 0;\n            // left: 0;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            background-color: $brand-green-light;\n            // background: rgba($brand-green-light, 0.8);\n            color: $text-inverse;\n\n            img {\n                width: auto;\n                height: auto;\n                max-width: 150px;\n                max-height: 90px;\n                object-fit: contain;\n            }\n        }\n    }\n\n    /* Card content */\n    .card-content {\n        padding: 1.25rem;\n\n        @media (max-width: 480px) {\n            padding: 1rem;\n        }\n\n        .date {\n            display: block;\n            font-size: 0.75rem;\n            color: $text-secondary;\n            margin-bottom: 0.5rem;\n            font-weight: 500;\n            text-transform: uppercase;\n            letter-spacing: 0.5px;\n        }\n\n        h4 {\n            margin: 0 0 0.75rem 0;\n            font-size: 1rem;\n            font-weight: 600;\n            line-height: 1.4;\n            color: $text-primary;\n\n            /* Limit to 2 lines */\n            display: -webkit-box;\n            -webkit-line-clamp: 2;\n            -webkit-box-orient: vertical;\n            overflow: hidden;\n        }\n\n        p {\n            margin: 0;\n            font-size: 0.875rem;\n            color: $text-secondary;\n            line-height: 1.5;\n\n            /* Limit to 3 lines */\n            display: -webkit-box;\n            -webkit-line-clamp: 3;\n            -webkit-box-orient: vertical;\n            overflow: hidden;\n        }\n    }\n\n    /* Archive card */\n    .carousel-card--archive {\n        background: $brand-red-primary;\n        color: $text-inverse;\n\n        .archive-content {\n            display: flex;\n            flex-direction: column;\n            align-items: center;\n            justify-content: center;\n            text-align: center;\n            padding: 2rem 1.5rem;\n            height: 100%;\n            min-height: 300px;\n\n            @media (max-width: 480px) {\n                padding: 1.5rem 1rem;\n                min-height: 260px;\n            }\n        }\n\n        .archive-icon {\n            margin-bottom: 1rem;\n            opacity: 0.9;\n\n            svg {\n                width: 48px;\n                height: 48px;\n            }\n        }\n\n        h4 {\n            margin: 0 0 0.75rem 0;\n            font-size: 1.25rem;\n            font-weight: 600;\n            color: white;\n        }\n\n        p {\n            margin: 0 0 1rem 0;\n            font-size: 0.875rem;\n            opacity: 0.9;\n            line-height: 1.5;\n            color: white;\n        }\n\n        .archive-cta {\n            font-size: 0.875rem;\n            font-weight: 500;\n            opacity: 0.8;\n            color: white;\n        }\n    }\n}", "@use 'colors' as *;\n\n.partners {\n    margin-top: 2rem;\n    padding: 3rem 0;\n    background-color: $bg-tertiary;\n    border-top: 1px solid $border-medium;\n\n}\n\n.partners-header {\n    text-align: center;\n}\n\n.partners-list {\n    display: flex;\n    justify-content: center;\n    align-items: center;;\n    flex-wrap: wrap;\n    gap: 3rem;\n    margin-top: 2rem;\n\n    @media screen and (max-width: 768px) {\n        width: 100%;\n        flex-direction: column;\n        align-items: center;\n        gap: 2rem;\n    }\n\n    a {\n        display: block;\n        text-decoration: none;\n        color: inherit;\n        cursor: pointer;\n        transition: transform 0.3s ease;\n\n        &:hover {\n            transform: scale(1.05);\n        }\n    }\n\n    .partner-name {\n        font-weight: bold;\n        font-size: 1.2rem;\n    }\n\n    img {\n        max-width: 150px;\n        max-height: 60px;\n        filter: grayscale(100%);\n        width: 100%;\n        height: auto;\n        object-fit: contain;\n    }\n}\n\nfooter {\n    background-color: $brand-green-primary;\n    padding: 4rem 1rem;\n    color: $text-inverse;\n\n    .container {\n        display: grid;\n        grid-template-columns: 1fr 1fr 1fr;\n        gap: 5rem;\n        grid-template-areas:\n            \"info contact finance\"\n            \"copyright copyright copyright\";\n        @media (max-width: 768px) {\n        grid-template-columns: 1fr;\n        grid-template-areas:\n            \"info\"\n            \"contact\"\n            \"finance\"\n            \"copyright\";\n    }\n    }\n\n    .footer-info {\n        h3 {\n            font-size: 1.25rem;\n            font-weight: 600;\n            margin: 0;\n            color: $text-inverse;\n        }\n\n        .contact-item {\n            display: flex;\n            align-items: flex-start;\n            gap: 1rem;\n\n            .contact-icon {\n                display: flex;\n                align-items: center;\n                justify-content: center;\n                width: 24px;\n                height: 24px;\n                color: $brand-green-light;\n                flex-shrink: 0;\n                margin-top: 0.125rem; // Slight offset to align with text\n\n                svg {\n                    width: 16px;\n                    height: 16px;\n                }\n            }\n\n            .info {\n\n                p {\n                    margin: 0 0 0.5rem 0;\n                    font-size: 0.875rem;\n                    font-weight: 600;\n                    color: $text-inverse;\n                }\n\n                address {\n                    font-style: normal;\n\n                    p {\n                        margin: 0;\n                        font-size: 0.875rem;\n                        line-height: 1.5;\n                        color: rgba($text-inverse, 0.8);\n                        font-weight: 400;\n                    }\n                }\n\n                a {\n                    color: rgba($text-inverse, 0.9);\n                    text-decoration: none;\n                    font-size: 0.875rem;\n                    line-height: 1.5;\n                    font-weight: 400;\n                    transition: color 0.3s ease;\n\n                    &:hover {\n                        color: $brand-green-light;\n                        text-decoration: underline;\n                    }\n                }\n            }\n        }\n\n    }\n\n    .club-info {\n        grid-area: info;\n        display: flex;\n        flex-direction: column;\n        gap: 1.5rem;\n\n        .footer-logo {\n            max-width: 120px;\n            height: auto;\n            filter: brightness(0) invert(1); // Invert logo to white\n        }\n\n        h3 {\n            font-size: 1.25rem;\n            font-weight: 600;\n            margin: 0;\n            color: $text-inverse;\n        }\n\n        > p {\n            font-size: 0.9rem;\n            line-height: 1.6;\n            color: rgba($text-inverse, 0.8);\n            margin: 0;\n        }\n\n        .social-links {\n            margin-top: 1rem;\n\n            h4 {\n                font-size: 1rem;\n                font-weight: 600;\n                margin: 0 0 1rem 0;\n                color: $text-inverse;\n            }\n\n            .social-list {\n                display: flex;\n                flex-direction: column;\n                gap: 0.75rem;\n\n                a {\n                    display: flex;\n                    align-items: center;\n                    gap: 0.75rem;\n                    color: $text-inverse;\n                    text-decoration: none;\n                    font-size: 0.875rem;\n                    font-weight: 500;\n                    transition: all 0.3s ease;\n\n                    &:hover {\n                        color: $brand-green-light;\n\n                        .social-icon {\n                            background-color: $brand-green-light;\n                            color: $brand-green-primary;\n                            transform: scale(1.05);\n                        }\n                    }\n\n                    .social-icon {\n                        display: flex;\n                        align-items: center;\n                        justify-content: center;\n                        width: 36px;\n                        height: 36px;\n                        background-color: rgba($text-inverse, 0.1);\n                        border-radius: 10px; // Squircle effect\n                        color: $text-inverse;\n                        transition: all 0.3s ease;\n                        flex-shrink: 0;\n\n                        svg {\n                            width: 18px;\n                            height: 18px;\n                        }\n                    }\n\n                    span {\n                        font-size: 0.875rem;\n                    }\n                }\n            }\n        }\n    }\n\n    .contact {\n        grid-area: contact;\n        display: flex;\n        flex-direction: column;\n        gap: 1.5rem;\n\n    }\n\n    .finance {\n        grid-area: finance;\n        display: flex;\n        flex-direction: column;\n        gap: 1.5rem;\n    }\n\n    .copyright {\n        grid-area: copyright;\n        display: flex;\n        flex-direction: column;\n        gap: 1rem;\n        margin-top: 2rem;\n        padding-top: 2rem;\n        border-top: 1px solid rgba($text-inverse, 0.2);\n\n        p {\n            font-size: 0.875rem;\n            line-height: 1.5;\n            color: rgba($text-inverse, 0.7);\n            margin: 0;\n        }\n\n        div {\n            display: flex;\n            align-items: center;\n            justify-content: space-between;\n            gap: 1rem;\n\n            @media screen and (max-width: 768px) {\n                flex-direction: column;\n                gap: 0.5rem;\n            }\n\n            a {\n                color: rgba($text-inverse, 0.8);\n                text-decoration: none;\n                font-size: 0.875rem;\n                line-height: 1.5;\n                font-weight: 400;\n                transition: color 0.3s ease;\n\n                &:hover {\n                    color: $brand-green-light;\n                    text-decoration: underline;\n                }\n            }\n\n            span {\n                color: rgba($text-inverse, 0.5);\n                font-size: 0.875rem;\n            }\n        }\n    }\n\n}\n"], "names": [], "sourceRoot": ""}