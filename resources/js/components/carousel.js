/**
 * Modern Carousel - Mobile-First Implementation
 * Uses native scrolling with scroll-snap for optimal mobile performance
 * Supports multiple carousels on the same page including calendar pills
 */

export function initNewsCarousel() {
  // Find all carousels on the page (news, events, calendar pills)
  const carousels = document.querySelectorAll('[data-carousel]');

  if (carousels.length === 0) return;

  // Initialize each carousel
  carousels.forEach(carousel => {
    initSingleCarousel(carousel);
  });
}

function initSingleCarousel(carousel) {
  const carouselWrapper = carousel.closest('.carousel-wrapper') || carousel.closest('.pills-wrapper');
  if (!carouselWrapper) return;

  const prevBtn = carouselWrapper.querySelector('.carousel-btn--prev');
  const nextBtn = carouselWrapper.querySelector('.carousel-btn--next');

  // Calendar pills don't have navigation buttons, so they're optional
  const hasNavButtons = prevBtn && nextBtn;

  // Support both carousel cards and event pills
  const cards = carousel.querySelectorAll('.carousel-card, .event-pill');
  if (cards.length === 0) return;

  let cardWidth = 0;
  let gap = 0;
  let visibleCards = 1;

  // Calculate dimensions and visible cards
  function calculateDimensions() {
    const firstCard = cards[0];
    if (!firstCard) return;

    cardWidth = firstCard.offsetWidth;
    const computedStyle = getComputedStyle(carousel);
    gap = parseInt(computedStyle.gap) || 24;

    // Calculate visible cards based on container width
    const containerWidth = carousel.parentElement.offsetWidth;
    visibleCards = Math.floor(containerWidth / (cardWidth + gap));

    // Ensure at least 1 card is visible
    if (visibleCards < 1) visibleCards = 1;

    updateButtonStates();
  }

  // Update navigation button states and gradient effects
  function updateButtonStates() {
    const scrollLeft = carousel.scrollLeft;
    const maxScroll = carousel.scrollWidth - carousel.clientWidth;

    // Only update button states if navigation buttons exist
    if (hasNavButtons) {
      prevBtn.disabled = scrollLeft <= 0;
      nextBtn.disabled = scrollLeft >= maxScroll - 1; // -1 for rounding errors
    }

    // Add/remove scrolled class for gradient effects
    if (scrollLeft > 10) {
      carouselWrapper.classList.add('scrolled');
    } else {
      carouselWrapper.classList.remove('scrolled');
    }
  }

  // Smooth scroll to position
  function scrollToPosition(position) {
    carousel.scrollTo({
      left: position,
      behavior: 'smooth'
    });
  }

  // Navigate to previous cards
  function goToPrevious() {
    const scrollAmount = (cardWidth + gap) * Math.min(visibleCards, 2);
    const newPosition = Math.max(0, carousel.scrollLeft - scrollAmount);
    scrollToPosition(newPosition);
  }

  // Navigate to next cards
  function goToNext() {
    const scrollAmount = (cardWidth + gap) * Math.min(visibleCards, 2);
    const maxScroll = carousel.scrollWidth - carousel.clientWidth;
    const newPosition = Math.min(maxScroll, carousel.scrollLeft + scrollAmount);
    scrollToPosition(newPosition);
  }

  // Handle keyboard navigation
  function handleKeydown(e) {
    if (e.target.closest('.news-carousel') === carouselWrapper.closest('.news-carousel')) {
      switch (e.key) {
        case 'ArrowLeft':
          e.preventDefault();
          goToPrevious();
          break;
        case 'ArrowRight':
          e.preventDefault();
          goToNext();
          break;
      }
    }
  }

  // Event listeners - only add if navigation buttons exist
  if (hasNavButtons) {
    prevBtn.addEventListener('click', goToPrevious);
    nextBtn.addEventListener('click', goToNext);

    // Keyboard navigation
    document.addEventListener('keydown', handleKeydown);
  }

  // Update button states on scroll (always needed for gradient effects)
  carousel.addEventListener('scroll', updateButtonStates, { passive: true });

  // Recalculate on resize
  window.addEventListener('resize', () => {
    calculateDimensions();
    // Small delay to ensure layout is complete
    setTimeout(updateButtonStates, 100);
  });

  // Initialize
  calculateDimensions();

  // Initial button state update after a short delay
  setTimeout(updateButtonStates, 100);
}
