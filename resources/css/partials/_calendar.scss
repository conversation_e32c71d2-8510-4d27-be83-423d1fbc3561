@use 'colors' as *;

// HOMEPAGE

.calendar-event {
    display: grid;
    grid-template-columns: 1fr 3fr;
    grid-template-areas: "event-date event-details";
    align-items: flex-start;
    gap: 1rem;
    width: 100%;
    height: 100%;
    .event-date {
        grid-area: event-date;
        margin: 1rem;
        padding: 1rem;
    }
    .event-details {
        grid-area: event-details;
        padding: 0.5rem;
    }
}
/* Date component with card-stack effect for multiple events */
    .event-date {
        flex-shrink: 0;
        background: $brand-green-primary;
        border-radius: 8px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: $text-inverse;
        position: relative;

        /* Card stack effect for multiple events */
        &.multiple-events {
            &::before {
                content: '';
                position: absolute;
                top: -3px;
                left: -3px;
                right: 3px;
                bottom: 3px;
                background: rgba($brand-green-primary, 0.3);
                border-radius: 8px;
                z-index: -1;
            }

            &::after {
                content: '';
                position: absolute;
                top: -6px;
                left: -6px;
                right: 6px;
                bottom: 6px;
                background: rgba($brand-green-primary, 0.15);
                border-radius: 8px;
                z-index: -2;
            }
        }
    }

// CALENDAR PAGE