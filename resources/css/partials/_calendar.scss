@use 'colors' as *;

// HOMEPAGE - PILL CALENDAR

.calendar-pills {
    padding: 2rem 0;
    background: $bg-secondary;

    .container {
        max-width: 1440px;
        margin: 0 auto;
        padding: 0 1rem;
    }

    .calendar-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;

        h3 {
            margin: 0;
            color: $text-brand;
            font-size: 1.5rem;
            font-weight: 600;
        }

        .calendar-view-all {
            color: $text-secondary;
            text-decoration: none;
            font-size: 0.875rem;
            font-weight: 500;
            transition: color 0.2s ease;

            &:hover {
                color: $interactive-primary;
            }
        }
    }

    .carousel-wrapper {
        position: relative;
        overflow: hidden;
    }

    .pills-scroll {
        display: flex;
        gap: 1rem;
        overflow-x: auto;
        scroll-behavior: smooth;
        scroll-snap-type: x mandatory;
        -webkit-overflow-scrolling: touch;
        scrollbar-width: none;
        -ms-overflow-style: none;
        padding: 0.5rem 0 1rem 0;

        &::-webkit-scrollbar {
            display: none;
        }

        @media (max-width: 640px) {
            gap: 0.75rem;
        }
    }

    .event-pill {
        flex: 0 0 auto;
        display: flex;
        align-items: center;
        gap: 0.75rem;
        background: $bg-primary;
        border-radius: 50px;
        padding: 0.75rem 1.25rem;
        text-decoration: none;
        color: inherit;
        transition: all 0.3s ease;
        scroll-snap-align: start;
        position: relative;
        min-width: 200px;
        box-shadow: 0 2px 8px $shadow-light;

        @media (max-width: 640px) {
            min-width: 180px;
            padding: 0.625rem 1rem;
            gap: 0.625rem;
        }

        &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px $shadow-medium;
            background: $bg-primary;
        }

        &--multiple {
            background: linear-gradient(135deg, $bg-primary 0%, $brand-green-light 100%);

            &:hover {
                background: linear-gradient(135deg, $bg-primary 0%, $brand-green-light 100%);
            }
        }
    }

    .pill-date {
        flex-shrink: 0;
        background: $brand-green-primary;
        border-radius: 50%;
        width: 44px;
        height: 44px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: $text-inverse;

        @media (max-width: 640px) {
            width: 40px;
            height: 40px;
        }

        .pill-day {
            font-size: 0.875rem;
            font-weight: 700;
            line-height: 1;
        }

        .pill-month {
            font-size: 0.625rem;
            font-weight: 500;
            text-transform: uppercase;
            line-height: 1;
            margin-top: 1px;
        }
    }

    .pill-content {
        flex: 1;
        min-width: 0;

        h4 {
            margin: 0 0 0.25rem 0;
            font-size: 0.875rem;
            font-weight: 600;
            line-height: 1.3;
            color: $text-primary;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;

            @media (max-width: 640px) {
                font-size: 0.8125rem;
            }
        }

        .pill-meta {
            display: flex;
            gap: 0.5rem;
            font-size: 0.75rem;
            color: $text-secondary;
            line-height: 1.2;

            span {
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            span:not(:last-child)::after {
                content: '•';
                margin-left: 0.5rem;
                opacity: 0.5;
            }

            .pill-source {
                background: rgba($brand-green-primary, 0.1);
                color: $brand-green-primary;
                padding: 0.125rem 0.375rem;
                border-radius: 10px;
                font-size: 0.625rem;
                font-weight: 600;
                text-transform: uppercase;
                letter-spacing: 0.5px;

                &::before {
                    content: none; /* Remove the bullet point */
                }
            }
        }
    }

    .pill-indicator {
        flex-shrink: 0;
        background: $brand-red-primary;
        color: $text-inverse;
        border-radius: 50%;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.625rem;
        font-weight: 700;
        margin-left: 0.5rem;

        @media (max-width: 640px) {
            width: 18px;
            height: 18px;
            font-size: 0.5625rem;
        }
    }

    /* Navigation for calendar pills - reuse carousel styles */
    .carousel-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 0.5rem;
        padding: 1rem 0;
    }

    .carousel-nav {
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        gap: 0.5rem;

        @media (max-width: 640px) {
            display: none; /* Hide on mobile, rely on touch scrolling */
        }
    }
}

// CALENDAR PAGE - Legacy styles (if needed)

.calendar-event {
    display: grid;
    grid-template-columns: 1fr 3fr;
    grid-template-areas: "event-date event-details";
    align-items: flex-start;
    gap: 1rem;
    width: 100%;
    height: 100%;

    .event-date {
        grid-area: event-date;
        margin: 1rem;
        padding: 1rem;
        flex-shrink: 0;
        background: $brand-green-primary;
        border-radius: 8px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: $text-inverse;
        position: relative;

        /* Card stack effect for multiple events */
        &.multiple-events {
            &::before {
                content: '';
                position: absolute;
                top: -3px;
                left: -3px;
                right: 3px;
                bottom: 3px;
                background: rgba($brand-green-primary, 0.3);
                border-radius: 8px;
                z-index: -1;
            }

            &::after {
                content: '';
                position: absolute;
                top: -6px;
                left: -6px;
                right: 6px;
                bottom: 6px;
                background: rgba($brand-green-primary, 0.15);
                border-radius: 8px;
                z-index: -2;
            }
        }
    }

    .event-details {
        grid-area: event-details;
        padding: 0.5rem;
    }
}

// CALENDAR PAGE