@use 'colors' as *;

// HOMEPAGE
.news {
    margin-bottom: 3rem;

    .news-grid {
        display: grid;
        gap: 2rem;
        margin-bottom: 3rem;
        grid-template-columns: 1fr 1fr;
        grid-template-areas: 
        "news-featured news-list"
        "news-cta news-cta";

        /* Mobile: Stack vertically */
        @media (max-width: 768px) { 
            grid-template-columns: 1fr;
        }
    }
    .news-featured {
        grid-area: news-featured;
        h2 {
            margin-bottom: 1.5rem;
        }
    }
    .news-list {
        grid-area: news-list;
        h3 {
            margin-bottom: 1.5rem;
        }
    }
    .news-placeholder {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        background-color: $brand-green-light;

        svg {
            width: 2rem;
            height: 2rem;
            color: $brand-green-primary;
        }
    }

    img {
        width: 100%;
        height: auto;
        object-fit: cover;
        // border-radius: 1rem;
    }
    time {
        display: block;
        font-size: 0.875rem;
        opacity: 0.9;
        margin-bottom: 0.5rem;
        font-weight: 500;
    }
    h2 {
        margin: 0;
        font-size: 1.5rem;
        line-height: 1.3;

        @media (min-width: 768px) {
            font-size: 1.875rem;
        }
    }

    p {
        margin: 0.75rem 0 0 0;
        opacity: 0.9;
        line-height: 1.5;
    }
    a {
        color: black;
    }
    .news-listing {
        display: grid;
        gap: 1rem;
        grid-template-columns: 1fr 3fr;
        grid-template-areas: "news-image news-content";
        padding: 1rem;
        border-top: 1px solid black;
        transition: all 0.3s ease;

        &:last-child {
            border-bottom: 1px solid black;
        }

        &:hover {
            transform: translateY(-4px);
        }

        .news-image {
            grid-area: news-image;
            img {
                width: 100%;
                height: auto;
                object-fit: cover;
            }
        }
        .news-content {
            grid-area: news-content;
            h3 {
                margin-bottom: 1rem;
            }
            p {
                margin-bottom: 1rem;
            }
        }
    }
}
.news-cta {
    grid-area: news-cta;
    display: flex;
    justify-content: flex-end;
    margin-bottom: 2rem;
}


// NEWS LISTING PAGE


// NEWS STORY PAGE