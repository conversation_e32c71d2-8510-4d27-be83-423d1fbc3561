/* Highlighted Updates Section - 2:1 Layout with responsive mobile-first approach */

@use 'colors' as *;
.highlighted-updates {
    display: grid;
    gap: 2rem;
    margin-bottom: 3rem;

    /* Mobile: Stack vertically with calendar first */
    grid-template-columns: 1fr;
    grid-template-areas:
        "calendar"
        "news";

    /* Desktop: Side by side with 2:1 ratio */
    @media (min-width: 768px) {
        grid-template-columns: 1fr 1fr;
        grid-template-areas: "news calendar";
    }

    h2 {
        grid-column: 1 / -1;
        margin-bottom: 1.5rem;
    }
}

/* News Section - Featured article with overlay */
.highlighted-updates-news {
    grid-area: news;

    article {
        position: relative;
        height: 100%;
        border-radius: 16px;
        overflow: hidden;
        box-shadow: 0 4px 20px $shadow-light;
        transition: all 0.3s ease;

        &:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 30px $shadow-medium;
        }

        a {
            display: block;
            text-decoration: none;
            color: inherit;
            height: 100%;

            > div {
                position: relative;
                min-height: 300px;
                height: 100%;

                @media (min-width: 768px) {
                    min-height: 400px;
                }
            }

            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                position: absolute;
                top: 0;
                left: 0;
            }

            /* Fallback for missing images */
            > div > div:first-child:not(.highlighted-news-content) {
                background: $brand-green-primary;
                width: 100%;
                height: 100%;
                position: absolute;
                top: 0;
                left: 0;
            }
        }
    }
}

/* News content overlay */
.highlighted-news-content {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, $bg-overlay);
    color: $text-inverse;
    padding: 2rem;

    time {
        display: block;
        font-size: 0.875rem;
        opacity: 0.9;
        margin-bottom: 0.5rem;
        font-weight: 500;
    }

    h2 {
        margin: 0;
        font-size: 1.5rem;
        line-height: 1.3;

        @media (min-width: 768px) {
            font-size: 1.875rem;
        }
    }

    p {
        margin: 0.75rem 0 0 0;
        opacity: 0.9;
        line-height: 1.5;
    }
}

/* Calendar Section - Stacked event layout */
.highlighted-updates-calendar {
    grid-area: calendar;

    .calendar-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;

        h3 {
            margin: 0;
            color: $text-brand;
        }

        a {
            color: $text-accent;
            text-decoration: none;
            font-weight: 500;
            font-size: 0.875rem;

            &:hover {
                text-decoration: underline;
            }
        }
    }

    /* Events container - compact single container */
    .calendar-events {
        background: $bg-primary;
        border-radius: 12px;
        padding: 1rem;
        margin-bottom: 1rem;
    }

    /* Individual event items - compact layout */
    .calendar-event {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.75rem 0;
        text-decoration: none;
        color: inherit;
        border-bottom: 1px solid $border-light;
        transition: all 0.2s ease;

        &:last-child {
            border-bottom: none;
        }

        &:hover {
            background: rgba($brand-green-primary, 0.05);
            margin: 0 -1rem;
            padding-left: 1rem;
            padding-right: 1rem;
            border-radius: 8px;
        }
    }

    /* Date component with card-stack effect for multiple events */
    .event-date {
        flex-shrink: 0;
        width: 50px;
        height: 50px;
        background: $brand-green-primary;
        border-radius: 8px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: $text-inverse;
        position: relative;

        /* Card stack effect for multiple events */
        &.multiple-events {
            &::before {
                content: '';
                position: absolute;
                top: -3px;
                left: -3px;
                right: 3px;
                bottom: 3px;
                background: rgba($brand-green-primary, 0.3);
                border-radius: 8px;
                z-index: -1;
            }

            &::after {
                content: '';
                position: absolute;
                top: -6px;
                left: -6px;
                right: 6px;
                bottom: 6px;
                background: rgba($brand-green-primary, 0.15);
                border-radius: 8px;
                z-index: -2;
            }
        }

        span:first-child {
            font-size: 1.125rem;
            font-weight: 700;
            line-height: 1;
        }

        span:last-child {
            font-size: 0.625rem;
            font-weight: 500;
            text-transform: uppercase;
            opacity: 0.9;
        }
    }

    /* Event details */
    .event-details {
        flex: 1;
        min-width: 0;

        h4 {
            margin: 0 0 0.125rem 0;
            font-size: 0.875rem;
            font-weight: 600;
            color: $text-primary;
            line-height: 1.3;

            /* Truncate long titles */
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .event-meta {
            display: flex;
            gap: 0.5rem;

            span {
                font-size: 0.75rem;
                color: $text-secondary;

                &:first-child {
                    font-weight: 500;
                    color: $text-brand;
                }
            }
        }
    }

    /* "See full calendar" link - use secondary button */
    .calendar-footer {
        text-align: center;
        padding: 8px 0;
    }
}
